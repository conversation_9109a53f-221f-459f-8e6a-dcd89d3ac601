#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品信息管理系统
作者：学生姓名
学号：学号
日期：2024年

功能说明：
- 商品信息的增删改查
- 数据持久化（CSV、Excel、TXT）
- 数据统计与可视化
- 完整的GUI界面操作
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import csv
import json
import sqlite3
from datetime import datetime
import os
from dialogs import ProductDialog, SearchDialog

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ProductManagementSystem:
    """商品信息管理系统主类"""

    def __init__(self):
        """初始化系统"""
        self.root = tk.Tk()
        self.root.title("商品信息管理系统")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # 数据存储
        self.products_data = []
        self.current_file = None

        # 初始化数据库
        self.init_database()

        # 创建界面
        self.create_menu()
        self.create_main_interface()

        # 加载默认数据
        self.load_sample_data()

    def init_database(self):
        """初始化SQLite数据库"""
        try:
            self.conn = sqlite3.connect('products.db')
            cursor = self.conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    category TEXT NOT NULL,
                    price REAL NOT NULL,
                    stock INTEGER NOT NULL,
                    supplier TEXT,
                    description TEXT,
                    create_date TEXT,
                    update_date TEXT
                )
            ''')
            self.conn.commit()
        except Exception as e:
            messagebox.showerror("数据库错误", f"初始化数据库失败：{str(e)}")

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建", command=self.new_file, accelerator="Ctrl+N")
        file_menu.add_command(label="打开", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_command(label="保存", command=self.save_file, accelerator="Ctrl+S")
        file_menu.add_command(label="另存为", command=self.save_as_file, accelerator="Ctrl+Shift+S")
        file_menu.add_separator()
        file_menu.add_command(label="导入CSV", command=self.import_csv)
        file_menu.add_command(label="导入Excel", command=self.import_excel)
        file_menu.add_command(label="导出CSV", command=self.export_csv)
        file_menu.add_command(label="导出Excel", command=self.export_excel)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.quit_app, accelerator="Ctrl+Q")

        # 数据菜单
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="数据管理", menu=data_menu)
        data_menu.add_command(label="添加商品", command=self.add_product, accelerator="Ctrl+A")
        data_menu.add_command(label="编辑商品", command=self.edit_product, accelerator="Ctrl+E")
        data_menu.add_command(label="删除商品", command=self.delete_product, accelerator="Delete")
        data_menu.add_separator()
        data_menu.add_command(label="搜索商品", command=self.search_product, accelerator="Ctrl+F")
        data_menu.add_command(label="刷新数据", command=self.refresh_data, accelerator="F5")

        # 统计菜单
        stats_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="统计分析", menu=stats_menu)
        stats_menu.add_command(label="库存统计", command=self.show_stock_stats)
        stats_menu.add_command(label="价格分析", command=self.show_price_analysis)
        stats_menu.add_command(label="分类统计", command=self.show_category_stats)
        stats_menu.add_command(label="生成报表", command=self.generate_report)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

        # 绑定快捷键
        self.bind_shortcuts()

    def bind_shortcuts(self):
        """绑定快捷键"""
        self.root.bind('<Control-n>', lambda e: self.new_file())
        self.root.bind('<Control-o>', lambda e: self.open_file())
        self.root.bind('<Control-s>', lambda e: self.save_file())
        self.root.bind('<Control-Shift-S>', lambda e: self.save_as_file())
        self.root.bind('<Control-q>', lambda e: self.quit_app())
        self.root.bind('<Control-a>', lambda e: self.add_product())
        self.root.bind('<Control-e>', lambda e: self.edit_product())
        self.root.bind('<Delete>', lambda e: self.delete_product())
        self.root.bind('<Control-f>', lambda e: self.search_product())
        self.root.bind('<F5>', lambda e: self.refresh_data())

    def create_main_interface(self):
        """创建主界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建左侧面板（数据表格）
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 创建右侧面板（统计和操作）
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.config(width=300)

        # 创建数据表格区域
        self.create_data_table(left_frame)

        # 创建操作面板
        self.create_operation_panel(right_frame)

        # 创建统计面板
        self.create_statistics_panel(right_frame)

        # 创建状态栏
        self.create_status_bar()

    def create_data_table(self, parent):
        """创建数据表格"""
        # 表格标题
        table_label = ttk.Label(parent, text="商品信息列表", font=("Arial", 12, "bold"))
        table_label.pack(pady=(0, 5))

        # 搜索框架
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        self.search_entry.pack(side=tk.LEFT, padx=(5, 5))
        self.search_entry.bind('<KeyRelease>', self.on_search)

        ttk.Button(search_frame, text="清除", command=self.clear_search).pack(side=tk.LEFT)

        # 创建表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 定义列
        columns = ('ID', '商品名称', '分类', '价格', '库存', '供应商', '描述')

        # 创建Treeview
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # 定义列标题和宽度
        column_widths = {'ID': 50, '商品名称': 120, '分类': 80, '价格': 80, '库存': 60, '供应商': 100, '描述': 150}

        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_column(c))
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 绑定双击事件
        self.tree.bind('<Double-1>', lambda e: self.edit_product())

    def create_operation_panel(self, parent):
        """创建操作面板"""
        # 操作面板标题
        op_label = ttk.Label(parent, text="操作面板", font=("Arial", 10, "bold"))
        op_label.pack(pady=(0, 10))

        # 操作按钮框架
        button_frame = ttk.LabelFrame(parent, text="数据操作", padding=10)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 按钮样式配置
        button_width = 15

        ttk.Button(button_frame, text="添加商品", command=self.add_product, width=button_width).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="编辑商品", command=self.edit_product, width=button_width).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="删除商品", command=self.delete_product, width=button_width).pack(fill=tk.X, pady=2)
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=5)
        ttk.Button(button_frame, text="刷新数据", command=self.refresh_data, width=button_width).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="导出数据", command=self.export_csv, width=button_width).pack(fill=tk.X, pady=2)

    def create_statistics_panel(self, parent):
        """创建统计面板"""
        # 统计面板标题
        stats_label = ttk.Label(parent, text="统计信息", font=("Arial", 10, "bold"))
        stats_label.pack(pady=(0, 10))

        # 统计信息框架
        stats_frame = ttk.LabelFrame(parent, text="数据统计", padding=10)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        # 统计标签
        self.total_products_label = ttk.Label(stats_frame, text="总商品数: 0")
        self.total_products_label.pack(anchor=tk.W, pady=2)

        self.total_value_label = ttk.Label(stats_frame, text="总价值: ¥0.00")
        self.total_value_label.pack(anchor=tk.W, pady=2)

        self.low_stock_label = ttk.Label(stats_frame, text="低库存商品: 0")
        self.low_stock_label.pack(anchor=tk.W, pady=2)

        self.categories_label = ttk.Label(stats_frame, text="商品分类数: 0")
        self.categories_label.pack(anchor=tk.W, pady=2)

        # 统计按钮
        ttk.Separator(stats_frame, orient='horizontal').pack(fill=tk.X, pady=5)
        ttk.Button(stats_frame, text="库存统计", command=self.show_stock_stats, width=15).pack(fill=tk.X, pady=2)
        ttk.Button(stats_frame, text="价格分析", command=self.show_price_analysis, width=15).pack(fill=tk.X, pady=2)
        ttk.Button(stats_frame, text="分类统计", command=self.show_category_stats, width=15).pack(fill=tk.X, pady=2)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        self.status_label = ttk.Label(self.status_bar, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)

        self.time_label = ttk.Label(self.status_bar, text="", relief=tk.SUNKEN)
        self.time_label.pack(side=tk.RIGHT, padx=2, pady=2)

        # 更新时间
        self.update_time()

    def update_time(self):
        """更新状态栏时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    def load_sample_data(self):
        """加载示例数据"""
        sample_data = [
            {"name": "iPhone 15", "category": "电子产品", "price": 5999.0, "stock": 50, "supplier": "苹果公司", "description": "最新款智能手机"},
            {"name": "MacBook Pro", "category": "电子产品", "price": 12999.0, "stock": 20, "supplier": "苹果公司", "description": "专业笔记本电脑"},
            {"name": "Nike运动鞋", "category": "服装鞋帽", "price": 899.0, "stock": 100, "supplier": "Nike", "description": "舒适运动鞋"},
            {"name": "咖啡豆", "category": "食品饮料", "price": 89.0, "stock": 200, "supplier": "星巴克", "description": "优质阿拉比卡咖啡豆"},
            {"name": "办公椅", "category": "家具用品", "price": 1299.0, "stock": 30, "supplier": "宜家", "description": "人体工学办公椅"},
        ]

        for product in sample_data:
            product["create_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            product["update_date"] = product["create_date"]
            self.products_data.append(product)

        self.refresh_table()
        self.update_statistics()

    def refresh_table(self):
        """刷新表格数据"""
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 添加数据
        for i, product in enumerate(self.products_data):
            values = (
                i + 1,
                product.get("name", ""),
                product.get("category", ""),
                f"¥{product.get('price', 0):.2f}",
                product.get("stock", 0),
                product.get("supplier", ""),
                product.get("description", "")
            )
            self.tree.insert("", tk.END, values=values)

    def update_statistics(self):
        """更新统计信息"""
        if not self.products_data:
            self.total_products_label.config(text="总商品数: 0")
            self.total_value_label.config(text="总价值: ¥0.00")
            self.low_stock_label.config(text="低库存商品: 0")
            self.categories_label.config(text="商品分类数: 0")
            return

        total_products = len(self.products_data)
        total_value = sum(p.get("price", 0) * p.get("stock", 0) for p in self.products_data)
        low_stock_count = sum(1 for p in self.products_data if p.get("stock", 0) < 10)
        categories = len(set(p.get("category", "") for p in self.products_data))

        self.total_products_label.config(text=f"总商品数: {total_products}")
        self.total_value_label.config(text=f"总价值: ¥{total_value:.2f}")
        self.low_stock_label.config(text=f"低库存商品: {low_stock_count}")
        self.categories_label.config(text=f"商品分类数: {categories}")

    def on_search(self, event=None):
        """搜索功能"""
        search_text = self.search_var.get().lower()
        if not search_text:
            self.refresh_table()
            return

        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 搜索并显示匹配的商品
        filtered_data = []
        for product in self.products_data:
            if (search_text in product.get("name", "").lower() or
                search_text in product.get("category", "").lower() or
                search_text in product.get("supplier", "").lower() or
                search_text in product.get("description", "").lower()):
                filtered_data.append(product)

        # 显示搜索结果
        for i, product in enumerate(filtered_data):
            values = (
                i + 1,
                product.get("name", ""),
                product.get("category", ""),
                f"¥{product.get('price', 0):.2f}",
                product.get("stock", 0),
                product.get("supplier", ""),
                product.get("description", "")
            )
            self.tree.insert("", tk.END, values=values)

    def clear_search(self):
        """清除搜索"""
        self.search_var.set("")
        self.refresh_table()

    def sort_column(self, col):
        """排序列"""
        # 获取当前数据
        data = [(self.tree.set(child, col), child) for child in self.tree.get_children('')]

        # 根据列类型进行排序
        if col in ['价格']:
            # 价格列需要特殊处理
            data.sort(key=lambda x: float(x[0].replace('¥', '').replace(',', '')))
        elif col in ['库存', 'ID']:
            # 数字列
            data.sort(key=lambda x: int(x[0]))
        else:
            # 文本列
            data.sort(key=lambda x: x[0])

        # 重新排列
        for index, (val, child) in enumerate(data):
            self.tree.move(child, '', index)

    def get_selected_product_index(self):
        """获取选中商品的索引"""
        selection = self.tree.selection()
        if not selection:
            return None

        item = selection[0]
        values = self.tree.item(item, 'values')
        if not values:
            return None

        # 根据商品名称查找索引
        product_name = values[1]
        for i, product in enumerate(self.products_data):
            if product.get("name") == product_name:
                return i
        return None

    # 数据操作方法
    def add_product(self):
        """添加商品"""
        dialog = ProductDialog(self.root, "添加商品")
        if dialog.result:
            product = dialog.result
            product["create_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            product["update_date"] = product["create_date"]
            self.products_data.append(product)
            self.refresh_table()
            self.update_statistics()
            self.status_label.config(text=f"已添加商品: {product['name']}")

    def edit_product(self):
        """编辑商品"""
        index = self.get_selected_product_index()
        if index is None:
            messagebox.showwarning("警告", "请先选择要编辑的商品")
            return

        product = self.products_data[index].copy()
        dialog = ProductDialog(self.root, "编辑商品", product)
        if dialog.result:
            updated_product = dialog.result
            updated_product["create_date"] = product["create_date"]
            updated_product["update_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.products_data[index] = updated_product
            self.refresh_table()
            self.update_statistics()
            self.status_label.config(text=f"已更新商品: {updated_product['name']}")

    def delete_product(self):
        """删除商品"""
        index = self.get_selected_product_index()
        if index is None:
            messagebox.showwarning("警告", "请先选择要删除的商品")
            return

        product = self.products_data[index]
        result = messagebox.askyesno("确认删除", f"确定要删除商品 '{product['name']}' 吗？")
        if result:
            deleted_product = self.products_data.pop(index)
            self.refresh_table()
            self.update_statistics()
            self.status_label.config(text=f"已删除商品: {deleted_product['name']}")

    def search_product(self):
        """搜索商品对话框"""
        search_dialog = SearchDialog(self.root, self.products_data)
        if search_dialog.result:
            # 在表格中高亮显示搜索结果
            self.highlight_search_result(search_dialog.result)

    def highlight_search_result(self, product_name):
        """高亮显示搜索结果"""
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
            if values and values[1] == product_name:
                self.tree.selection_set(item)
                self.tree.focus(item)
                self.tree.see(item)
                break

    def refresh_data(self):
        """刷新数据"""
        self.refresh_table()
        self.update_statistics()
        self.status_label.config(text="数据已刷新")

    # 文件操作方法
    def new_file(self):
        """新建文件"""
        if self.products_data:
            result = messagebox.askyesnocancel("新建文件", "当前有未保存的数据，是否保存？")
            if result is None:  # 取消
                return
            elif result:  # 是
                self.save_file()

        self.products_data.clear()
        self.current_file = None
        self.refresh_table()
        self.update_statistics()
        self.root.title("商品信息管理系统 - 新文件")
        self.status_label.config(text="已创建新文件")

    def open_file(self):
        """打开文件"""
        file_path = filedialog.askopenfilename(
            title="打开文件",
            filetypes=[
                ("JSON文件", "*.json"),
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )

        if not file_path:
            return

        try:
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.products_data = json.load(f)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
                self.products_data = df.to_dict('records')
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path, engine='openpyxl')
                self.products_data = df.to_dict('records')

            self.current_file = file_path
            self.refresh_table()
            self.update_statistics()
            self.root.title(f"商品信息管理系统 - {os.path.basename(file_path)}")
            self.status_label.config(text=f"已打开文件: {os.path.basename(file_path)}")

        except Exception as e:
            messagebox.showerror("错误", f"打开文件失败：{str(e)}")

    def save_file(self):
        """保存文件"""
        if self.current_file:
            self._save_to_file(self.current_file)
        else:
            self.save_as_file()

    def save_as_file(self):
        """另存为文件"""
        file_path = filedialog.asksaveasfilename(
            title="保存文件",
            defaultextension=".json",
            filetypes=[
                ("JSON文件", "*.json"),
                ("CSV文件", "*.csv"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self._save_to_file(file_path)
            self.current_file = file_path
            self.root.title(f"商品信息管理系统 - {os.path.basename(file_path)}")

    def _save_to_file(self, file_path):
        """保存到指定文件"""
        try:
            if file_path.endswith('.json'):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.products_data, f, ensure_ascii=False, indent=2)
            elif file_path.endswith('.csv'):
                df = pd.DataFrame(self.products_data)
                df.to_csv(file_path, index=False, encoding='utf-8-sig')

            self.status_label.config(text=f"已保存文件: {os.path.basename(file_path)}")

        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败：{str(e)}")

    def import_csv(self):
        """导入CSV文件"""
        file_path = filedialog.askopenfilename(
            title="导入CSV文件",
            filetypes=[("CSV文件", "*.csv")]
        )

        if not file_path:
            return

        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            imported_data = df.to_dict('records')

            # 添加时间戳
            for product in imported_data:
                if 'create_date' not in product:
                    product['create_date'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                if 'update_date' not in product:
                    product['update_date'] = product['create_date']

            self.products_data.extend(imported_data)
            self.refresh_table()
            self.update_statistics()
            self.status_label.config(text=f"已导入 {len(imported_data)} 条记录")

        except Exception as e:
            messagebox.showerror("错误", f"导入CSV文件失败：{str(e)}")

    def import_excel(self):
        """导入Excel文件"""
        file_path = filedialog.askopenfilename(
            title="导入Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("Excel文件(旧版)", "*.xls")]
        )

        if not file_path:
            return

        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, engine='openpyxl')

            # 检查必要的列是否存在
            required_columns = ['name', 'category', 'price', 'stock']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                # 如果缺少必要列，尝试使用中文列名映射
                column_mapping = {
                    '商品名称': 'name',
                    '分类': 'category',
                    '价格': 'price',
                    '库存': 'stock',
                    '供应商': 'supplier',
                    '描述': 'description'
                }

                # 重命名列
                df = df.rename(columns=column_mapping)

                # 再次检查必要列
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    messagebox.showerror("错误",
                        f"Excel文件缺少必要的列: {', '.join(missing_columns)}\n"
                        f"请确保文件包含以下列: name(商品名称), category(分类), price(价格), stock(库存)")
                    return

            # 数据类型转换和验证
            try:
                df['price'] = pd.to_numeric(df['price'], errors='coerce')
                df['stock'] = pd.to_numeric(df['stock'], errors='coerce').astype('Int64')

                # 删除价格或库存为空的行
                df = df.dropna(subset=['name', 'category', 'price', 'stock'])

                # 确保价格和库存为非负数
                df = df[(df['price'] >= 0) & (df['stock'] >= 0)]

            except Exception as e:
                messagebox.showerror("错误", f"数据格式错误：{str(e)}")
                return

            if df.empty:
                messagebox.showwarning("警告", "Excel文件中没有有效的数据")
                return

            # 转换为字典列表
            imported_data = df.to_dict('records')

            # 添加时间戳和处理缺失字段
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            for product in imported_data:
                # 确保所有字段都存在
                product.setdefault('supplier', '')
                product.setdefault('description', '')
                product.setdefault('create_date', current_time)
                product.setdefault('update_date', current_time)

                # 转换数据类型
                product['price'] = float(product['price'])
                product['stock'] = int(product['stock'])

            # 添加到现有数据
            self.products_data.extend(imported_data)
            self.refresh_table()
            self.update_statistics()
            self.status_label.config(text=f"已从Excel导入 {len(imported_data)} 条记录")

            messagebox.showinfo("成功", f"成功导入 {len(imported_data)} 条商品记录")

        except Exception as e:
            messagebox.showerror("错误", f"导入Excel文件失败：{str(e)}")

    def export_csv(self):
        """导出CSV文件"""
        if not self.products_data:
            messagebox.showwarning("警告", "没有数据可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出CSV文件",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv")]
        )

        if file_path:
            try:
                df = pd.DataFrame(self.products_data)
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                self.status_label.config(text=f"已导出到: {os.path.basename(file_path)}")
                messagebox.showinfo("成功", f"数据已导出到 {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("错误", f"导出CSV文件失败：{str(e)}")

    def export_excel(self):
        """导出Excel文件"""
        if not self.products_data:
            messagebox.showwarning("警告", "没有数据可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")]
        )

        if file_path:
            try:
                df = pd.DataFrame(self.products_data)
                df.to_excel(file_path, index=False, engine='openpyxl')
                self.status_label.config(text=f"已导出到: {os.path.basename(file_path)}")
                messagebox.showinfo("成功", f"数据已导出到 {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("错误", f"导出Excel文件失败：{str(e)}")

    def quit_app(self):
        """退出应用"""
        if self.products_data:
            result = messagebox.askyesnocancel("退出", "有未保存的数据，是否保存后退出？")
            if result is None:  # 取消
                return
            elif result:  # 是
                self.save_file()

        if hasattr(self, 'conn'):
            self.conn.close()
        self.root.quit()

    # 统计分析方法
    def show_stock_stats(self):
        """显示库存统计"""
        if not self.products_data:
            messagebox.showwarning("警告", "没有数据可统计")
            return

        stats_window = StatsWindow(self.root, "库存统计", self.products_data, "stock")

    def show_price_analysis(self):
        """显示价格分析"""
        if not self.products_data:
            messagebox.showwarning("警告", "没有数据可分析")
            return

        stats_window = StatsWindow(self.root, "价格分析", self.products_data, "price")

    def show_category_stats(self):
        """显示分类统计"""
        if not self.products_data:
            messagebox.showwarning("警告", "没有数据可统计")
            return

        stats_window = StatsWindow(self.root, "分类统计", self.products_data, "category")

    def generate_report(self):
        """生成报表"""
        if not self.products_data:
            messagebox.showwarning("警告", "没有数据可生成报表")
            return

        report_window = ReportWindow(self.root, self.products_data)

    def show_help(self):
        """显示帮助信息"""
        help_text = """
商品信息管理系统使用说明

1. 数据管理：
   - 添加商品：点击"添加商品"按钮或按Ctrl+A
   - 编辑商品：选中商品后点击"编辑商品"或双击商品行
   - 删除商品：选中商品后点击"删除商品"或按Delete键
   - 搜索商品：在搜索框中输入关键词或按Ctrl+F打开搜索对话框

2. 文件操作：
   - 新建：Ctrl+N
   - 打开：Ctrl+O
   - 保存：Ctrl+S
   - 另存为：Ctrl+Shift+S
   - 导入/导出：支持CSV、Excel格式
   - 导入Excel：支持.xlsx和.xls格式，自动识别中英文列名

3. 统计分析：
   - 库存统计：查看库存分布情况
   - 价格分析：分析商品价格分布
   - 分类统计：统计各分类商品数量

4. 其他功能：
   - 数据排序：点击列标题进行排序
   - 实时统计：右侧面板显示实时统计信息
   - 状态栏：显示操作状态和当前时间
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("600x500")
        help_window.resizable(False, False)
        help_window.transient(self.root)

        # 居中显示
        x = (help_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (help_window.winfo_screenheight() // 2) - (500 // 2)
        help_window.geometry(f"600x500+{x}+{y}")

        # 创建文本框
        text_frame = ttk.Frame(help_window, padding=20)
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 10))
        text_widget.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.insert(tk.END, help_text)
        text_widget.configure(state=tk.DISABLED)

        # 关闭按钮
        ttk.Button(help_window, text="关闭", command=help_window.destroy).pack(pady=10)

    def show_about(self):
        """显示关于信息"""
        about_text = """
商品信息管理系统 v1.0

开发者：学生姓名
学号：学号
开发时间：2024年

功能特点：
✓ 完整的商品信息管理
✓ 多种文件格式支持
✓ 数据统计与可视化
✓ 友好的用户界面
✓ 数据持久化存储

技术栈：
- Python 3.x
- Tkinter (GUI)
- Pandas (数据处理)
- Matplotlib (图表)
- SQLite (数据库)

版权所有 © 2024
        """

        messagebox.showinfo("关于", about_text)

class StatsWindow:
    """统计窗口类"""

    def __init__(self, parent, title, data, stats_type):
        self.data = data
        self.stats_type = stats_type

        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("800x600")
        self.window.transient(parent)

        # 居中显示
        x = (self.window.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"800x600+{x}+{y}")

        # 创建界面
        self.create_widgets()

        # 生成统计图表
        self.generate_chart()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 图表框架
        self.chart_frame = ttk.Frame(main_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="刷新", command=self.generate_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出图片", command=self.export_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def generate_chart(self):
        """生成统计图表"""
        # 清除之前的图表
        for widget in self.chart_frame.winfo_children():
            widget.destroy()

        # 创建matplotlib图表
        fig, ax = plt.subplots(figsize=(10, 6))

        if self.stats_type == "stock":
            self.generate_stock_chart(ax)
        elif self.stats_type == "price":
            self.generate_price_chart(ax)
        elif self.stats_type == "category":
            self.generate_category_chart(ax)

        # 嵌入到Tkinter中
        canvas = FigureCanvasTkAgg(fig, self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def generate_stock_chart(self, ax):
        """生成库存统计图表"""
        stocks = [p.get('stock', 0) for p in self.data]
        names = [p.get('name', '') for p in self.data]

        # 创建柱状图
        bars = ax.bar(range(len(names)), stocks, color='skyblue', alpha=0.7)
        ax.set_xlabel('商品')
        ax.set_ylabel('库存数量')
        ax.set_title('商品库存统计')
        ax.set_xticks(range(len(names)))
        ax.set_xticklabels(names, rotation=45, ha='right')

        # 添加数值标签
        for bar, stock in zip(bars, stocks):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{stock}', ha='center', va='bottom')

        plt.tight_layout()

    def generate_price_chart(self, ax):
        """生成价格分析图表"""
        prices = [p.get('price', 0) for p in self.data]
        names = [p.get('name', '') for p in self.data]

        # 创建散点图
        ax.scatter(range(len(names)), prices, color='red', alpha=0.6, s=100)
        ax.set_xlabel('商品')
        ax.set_ylabel('价格 (¥)')
        ax.set_title('商品价格分析')
        ax.set_xticks(range(len(names)))
        ax.set_xticklabels(names, rotation=45, ha='right')

        # 添加平均价格线
        avg_price = sum(prices) / len(prices) if prices else 0
        ax.axhline(y=avg_price, color='green', linestyle='--',
                  label=f'平均价格: ¥{avg_price:.2f}')
        ax.legend()

        plt.tight_layout()

    def generate_category_chart(self, ax):
        """生成分类统计图表"""
        categories = {}
        for p in self.data:
            category = p.get('category', '未分类')
            categories[category] = categories.get(category, 0) + 1

        # 创建饼图
        labels = list(categories.keys())
        sizes = list(categories.values())
        colors = plt.cm.Set3(range(len(labels)))

        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                         autopct='%1.1f%%', startangle=90)
        ax.set_title('商品分类统计')

        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

    def export_chart(self):
        """导出图表"""
        file_path = filedialog.asksaveasfilename(
            title="导出图表",
            defaultextension=".png",
            filetypes=[("PNG图片", "*.png"), ("JPG图片", "*.jpg"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 获取当前图表
                for widget in self.chart_frame.winfo_children():
                    if isinstance(widget, tk.Widget):
                        widget.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                        messagebox.showinfo("成功", f"图表已导出到 {os.path.basename(file_path)}")
                        break
            except Exception as e:
                messagebox.showerror("错误", f"导出图表失败：{str(e)}")

class ReportWindow:
    """报表窗口类"""

    def __init__(self, parent, data):
        self.data = data

        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("数据报表")
        self.window.geometry("700x500")
        self.window.transient(parent)

        # 居中显示
        x = (self.window.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.window.winfo_screenheight() // 2) - (500 // 2)
        self.window.geometry(f"700x500+{x}+{y}")

        # 创建界面
        self.create_widgets()

        # 生成报表
        self.generate_report()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 报表文本框
        self.report_text = tk.Text(main_frame, wrap=tk.WORD, font=("Courier", 10))
        self.report_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.report_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.report_text.configure(yscrollcommand=scrollbar.set)

        # 按钮框架
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="导出报表", command=self.export_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def generate_report(self):
        """生成报表内容"""
        report = []
        report.append("=" * 60)
        report.append("商品信息管理系统 - 数据报表")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 基本统计
        total_products = len(self.data)
        total_value = sum(p.get('price', 0) * p.get('stock', 0) for p in self.data)
        avg_price = sum(p.get('price', 0) for p in self.data) / total_products if total_products > 0 else 0
        total_stock = sum(p.get('stock', 0) for p in self.data)

        report.append("基本统计信息:")
        report.append("-" * 30)
        report.append(f"商品总数: {total_products}")
        report.append(f"库存总量: {total_stock}")
        report.append(f"总价值: ¥{total_value:.2f}")
        report.append(f"平均价格: ¥{avg_price:.2f}")
        report.append("")

        # 分类统计
        categories = {}
        for p in self.data:
            category = p.get('category', '未分类')
            if category not in categories:
                categories[category] = {'count': 0, 'value': 0}
            categories[category]['count'] += 1
            categories[category]['value'] += p.get('price', 0) * p.get('stock', 0)

        report.append("分类统计:")
        report.append("-" * 30)
        for category, stats in categories.items():
            report.append(f"{category}: {stats['count']}件, 价值¥{stats['value']:.2f}")
        report.append("")

        # 库存预警
        low_stock_products = [p for p in self.data if p.get('stock', 0) < 10]
        if low_stock_products:
            report.append("库存预警 (库存<10):")
            report.append("-" * 30)
            for p in low_stock_products:
                report.append(f"{p.get('name', '')}: {p.get('stock', 0)}件")
            report.append("")

        # 高价值商品
        high_value_products = sorted(self.data, key=lambda x: x.get('price', 0) * x.get('stock', 0), reverse=True)[:5]
        report.append("高价值商品 (TOP 5):")
        report.append("-" * 30)
        for i, p in enumerate(high_value_products, 1):
            value = p.get('price', 0) * p.get('stock', 0)
            report.append(f"{i}. {p.get('name', '')}: ¥{value:.2f}")

        # 显示报表
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, "\n".join(report))

    def export_report(self):
        """导出报表"""
        file_path = filedialog.asksaveasfilename(
            title="导出报表",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.report_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"报表已导出到 {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"导出报表失败：{str(e)}")

if __name__ == "__main__":
    app = ProductManagementSystem()
    app.root.mainloop()
