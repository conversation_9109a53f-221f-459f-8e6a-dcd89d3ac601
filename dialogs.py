#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话框模块
包含商品编辑对话框、搜索对话框等
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class ProductDialog:
    """商品编辑对话框"""
    
    def __init__(self, parent, title, product=None):
        self.result = None
        self.product = product or {}
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 如果是编辑模式，填充数据
        if self.product:
            self.fill_data()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"400x500+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 商品名称
        ttk.Label(main_frame, text="商品名称 *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=30)
        self.name_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # 商品分类
        ttk.Label(main_frame, text="商品分类 *:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(main_frame, textvariable=self.category_var, width=27)
        self.category_combo['values'] = ('电子产品', '服装鞋帽', '食品饮料', '家具用品', '图书文具', '运动户外', '美妆护肤', '其他')
        self.category_combo.grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # 商品价格
        ttk.Label(main_frame, text="商品价格 *:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.price_var = tk.StringVar()
        self.price_entry = ttk.Entry(main_frame, textvariable=self.price_var, width=30)
        self.price_entry.grid(row=2, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # 库存数量
        ttk.Label(main_frame, text="库存数量 *:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.stock_var = tk.StringVar()
        self.stock_entry = ttk.Entry(main_frame, textvariable=self.stock_var, width=30)
        self.stock_entry.grid(row=3, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # 供应商
        ttk.Label(main_frame, text="供应商:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.supplier_var = tk.StringVar()
        self.supplier_entry = ttk.Entry(main_frame, textvariable=self.supplier_var, width=30)
        self.supplier_entry.grid(row=4, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # 商品描述
        ttk.Label(main_frame, text="商品描述:").grid(row=5, column=0, sticky=tk.W+tk.N, pady=5)
        self.description_text = tk.Text(main_frame, width=30, height=6, wrap=tk.WORD)
        self.description_text.grid(row=5, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # 滚动条
        desc_scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.description_text.yview)
        desc_scrollbar.grid(row=5, column=2, sticky=tk.N+tk.S, pady=5)
        self.description_text.configure(yscrollcommand=desc_scrollbar.set)
        
        # 必填项提示
        ttk.Label(main_frame, text="* 为必填项", foreground="red", font=("Arial", 8)).grid(row=6, column=0, columnspan=2, sticky=tk.W, pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # 配置列权重
        main_frame.columnconfigure(1, weight=1)
        
        # 绑定回车键
        self.dialog.bind('<Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
        
        # 焦点设置
        self.name_entry.focus()
    
    def fill_data(self):
        """填充编辑数据"""
        self.name_var.set(self.product.get("name", ""))
        self.category_var.set(self.product.get("category", ""))
        self.price_var.set(str(self.product.get("price", "")))
        self.stock_var.set(str(self.product.get("stock", "")))
        self.supplier_var.set(self.product.get("supplier", ""))
        self.description_text.insert(tk.END, self.product.get("description", ""))
    
    def validate_input(self):
        """验证输入数据"""
        # 检查必填项
        if not self.name_var.get().strip():
            messagebox.showerror("错误", "请输入商品名称")
            self.name_entry.focus()
            return False
        
        if not self.category_var.get().strip():
            messagebox.showerror("错误", "请选择商品分类")
            self.category_combo.focus()
            return False
        
        # 验证价格
        try:
            price = float(self.price_var.get())
            if price < 0:
                messagebox.showerror("错误", "价格不能为负数")
                self.price_entry.focus()
                return False
        except ValueError:
            messagebox.showerror("错误", "请输入有效的价格")
            self.price_entry.focus()
            return False
        
        # 验证库存
        try:
            stock = int(self.stock_var.get())
            if stock < 0:
                messagebox.showerror("错误", "库存不能为负数")
                self.stock_entry.focus()
                return False
        except ValueError:
            messagebox.showerror("错误", "请输入有效的库存数量")
            self.stock_entry.focus()
            return False
        
        return True
    
    def ok_clicked(self):
        """确定按钮点击"""
        if self.validate_input():
            self.result = {
                "name": self.name_var.get().strip(),
                "category": self.category_var.get().strip(),
                "price": float(self.price_var.get()),
                "stock": int(self.stock_var.get()),
                "supplier": self.supplier_var.get().strip(),
                "description": self.description_text.get(1.0, tk.END).strip()
            }
            self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()

class SearchDialog:
    """搜索对话框"""
    
    def __init__(self, parent, products_data):
        self.result = None
        self.products_data = products_data
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("搜索商品")
        self.dialog.geometry("500x400")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 搜索框架
        search_frame = ttk.LabelFrame(main_frame, text="搜索条件", padding=10)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 搜索关键词
        ttk.Label(search_frame, text="关键词:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.keyword_var = tk.StringVar()
        self.keyword_entry = ttk.Entry(search_frame, textvariable=self.keyword_var, width=30)
        self.keyword_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # 搜索按钮
        ttk.Button(search_frame, text="搜索", command=self.search).grid(row=0, column=2, padx=(10, 0))
        
        # 结果列表框架
        result_frame = ttk.LabelFrame(main_frame, text="搜索结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 结果列表
        self.result_listbox = tk.Listbox(result_frame, height=10)
        self.result_listbox.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="选择", command=self.select_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # 配置权重
        search_frame.columnconfigure(1, weight=1)
        
        # 绑定事件
        self.keyword_entry.bind('<Return>', lambda e: self.search())
        self.result_listbox.bind('<Double-1>', lambda e: self.select_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
        
        # 焦点设置
        self.keyword_entry.focus()
        
        # 初始显示所有商品
        self.show_all_products()
    
    def show_all_products(self):
        """显示所有商品"""
        self.result_listbox.delete(0, tk.END)
        for product in self.products_data:
            display_text = f"{product.get('name', '')} - {product.get('category', '')} - ¥{product.get('price', 0):.2f}"
            self.result_listbox.insert(tk.END, display_text)
    
    def search(self):
        """执行搜索"""
        keyword = self.keyword_var.get().lower().strip()
        self.result_listbox.delete(0, tk.END)
        
        if not keyword:
            self.show_all_products()
            return
        
        # 搜索匹配的商品
        for product in self.products_data:
            if (keyword in product.get("name", "").lower() or
                keyword in product.get("category", "").lower() or
                keyword in product.get("supplier", "").lower() or
                keyword in product.get("description", "").lower()):
                display_text = f"{product.get('name', '')} - {product.get('category', '')} - ¥{product.get('price', 0):.2f}"
                self.result_listbox.insert(tk.END, display_text)
    
    def select_clicked(self):
        """选择按钮点击"""
        selection = self.result_listbox.curselection()
        if selection:
            selected_text = self.result_listbox.get(selection[0])
            # 提取商品名称（第一个 " - " 之前的部分）
            product_name = selected_text.split(" - ")[0]
            self.result = product_name
            self.dialog.destroy()
        else:
            messagebox.showwarning("警告", "请选择一个商品")
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()
