# 商品信息管理系统

基于Python Tkinter开发的商品信息管理系统，实现了完整的商品信息管理功能。

## 功能特点

### 核心功能
- ✅ 商品信息的增、删、改、查
- ✅ 数据持久化存储（JSON、CSV、Excel、SQLite）
- ✅ 实时搜索和过滤
- ✅ 数据统计与可视化分析
- ✅ 报表生成和导出

### 界面设计
- ✅ 完整的菜单栏系统
- ✅ 数据表格展示
- ✅ 实时统计面板
- ✅ 状态栏显示
- ✅ 多种对话框交互

### 数据管理
- ✅ 支持从键盘输入数据
- ✅ 支持从文件导入数据（CSV、JSON）
- ✅ 支持导出到多种格式（CSV、Excel、TXT）
- ✅ SQLite数据库存储
- ✅ 数据验证和错误处理

### 统计分析
- ✅ 库存统计图表
- ✅ 价格分析图表
- ✅ 分类统计饼图
- ✅ 数据报表生成
- ✅ 图表导出功能

## 技术架构

- **GUI框架**: Tkinter + ttk
- **数据处理**: Pandas
- **图表可视化**: Matplotlib
- **数据库**: SQLite
- **文件格式**: JSON, CSV, Excel

## 安装和运行

### 环境要求
- Python 3.7+
- 所需依赖包（见requirements.txt）

### 安装步骤

1. 克隆或下载项目文件
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

3. 运行程序：
```bash
python main.py
```

## 使用说明

### 基本操作
- **添加商品**: 点击"添加商品"按钮或按Ctrl+A
- **编辑商品**: 选中商品后点击"编辑商品"或双击商品行
- **删除商品**: 选中商品后点击"删除商品"或按Delete键
- **搜索商品**: 在搜索框中输入关键词或按Ctrl+F

### 文件操作
- **新建**: Ctrl+N
- **打开**: Ctrl+O  
- **保存**: Ctrl+S
- **另存为**: Ctrl+Shift+S

### 数据导入导出
- 支持CSV文件导入导出
- 支持Excel文件导出
- 支持JSON格式保存

### 统计分析
- **库存统计**: 查看各商品库存分布
- **价格分析**: 分析商品价格分布和平均价格
- **分类统计**: 统计各分类商品数量占比
- **报表生成**: 生成详细的数据分析报表

## 项目结构

```
GUI/
├── main.py              # 主程序文件
├── dialogs.py           # 对话框模块
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
└── products.db         # SQLite数据库文件（运行后生成）
```

## 代码特点

### 模块化设计
- 主程序类：`ProductManagementSystem`
- 对话框类：`ProductDialog`, `SearchDialog`
- 统计窗口类：`StatsWindow`, `ReportWindow`

### 高内聚低耦合
- 每个类职责单一明确
- 模块间通过清晰的接口交互
- 数据和界面分离

### 错误处理
- 完善的输入验证
- 异常捕获和用户友好的错误提示
- 数据完整性检查

### 用户体验
- 直观的界面布局
- 丰富的快捷键支持
- 实时状态反馈
- 操作确认对话框

## 扩展功能

系统具有良好的扩展性，可以轻松添加：
- 更多的数据字段
- 新的统计图表类型
- 数据库备份和恢复
- 用户权限管理
- 网络数据同步

## 开发信息

- **开发者**: 学生姓名
- **学号**: 学号  
- **开发时间**: 2024年
- **版本**: v1.0

## 许可证

本项目仅用于学习和教育目的。
