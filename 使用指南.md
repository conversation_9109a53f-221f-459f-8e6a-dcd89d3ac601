# 商品信息管理系统使用指南

## 快速开始

### 1. 启动程序
双击运行 `main.py` 或在命令行中执行：
```bash
python main.py
```

### 2. 界面介绍
程序启动后，您将看到：
- **左侧**：商品信息表格，显示所有商品数据
- **右侧**：操作面板和统计信息面板
- **顶部**：菜单栏，包含所有功能选项
- **底部**：状态栏，显示操作状态和当前时间

## 基本操作

### 商品管理

#### 添加商品
1. 点击右侧"添加商品"按钮，或按快捷键 `Ctrl+A`
2. 在弹出的对话框中填写商品信息：
   - **商品名称**（必填）
   - **商品分类**（必填，可从下拉列表选择）
   - **商品价格**（必填，数字格式）
   - **库存数量**（必填，整数）
   - **供应商**（可选）
   - **商品描述**（可选）
3. 点击"确定"保存，或"取消"放弃

#### 编辑商品
1. 在表格中选中要编辑的商品行
2. 双击该行，或点击"编辑商品"按钮，或按快捷键 `Ctrl+E`
3. 在弹出的对话框中修改信息
4. 点击"确定"保存修改

#### 删除商品
1. 在表格中选中要删除的商品行
2. 点击"删除商品"按钮，或按 `Delete` 键
3. 在确认对话框中点击"是"确认删除

#### 搜索商品
- **实时搜索**：在表格上方的搜索框中输入关键词，系统会实时过滤显示结果
- **高级搜索**：按快捷键 `Ctrl+F` 打开搜索对话框，可以查看详细的搜索结果

### 文件操作

#### 新建文件
- 菜单：文件 → 新建
- 快捷键：`Ctrl+N`
- 清空当前数据，开始新的数据管理

#### 打开文件
- 菜单：文件 → 打开
- 快捷键：`Ctrl+O`
- 支持打开 JSON 和 CSV 格式的文件

#### 保存文件
- 菜单：文件 → 保存
- 快捷键：`Ctrl+S`
- 保存到当前文件，如果是新文件会提示选择保存位置

#### 另存为
- 菜单：文件 → 另存为
- 快捷键：`Ctrl+Shift+S`
- 选择新的文件名和位置保存

#### 导入数据
- **导入CSV**：菜单 → 文件 → 导入CSV
  - 从CSV文件导入商品数据，会添加到现有数据中
- **导入Excel**：菜单 → 文件 → 导入Excel
  - 支持.xlsx和.xls格式
  - 自动识别中英文列名
  - 支持列名映射：商品名称→name, 分类→category, 价格→price, 库存→stock
  - 自动数据验证和类型转换

#### 导出数据
- **导出CSV**：菜单 → 文件 → 导出CSV
- **导出Excel**：菜单 → 文件 → 导出Excel
- 将当前数据导出为指定格式

## 统计分析功能

### 实时统计
右侧统计面板会实时显示：
- 总商品数
- 总价值（价格×库存的总和）
- 低库存商品数（库存<10的商品）
- 商品分类数

### 图表分析

#### 库存统计
- 菜单：统计分析 → 库存统计
- 显示各商品的库存数量柱状图
- 可以直观看出哪些商品库存充足，哪些需要补货

#### 价格分析
- 菜单：统计分析 → 价格分析
- 显示商品价格分布散点图
- 包含平均价格线，帮助分析价格结构

#### 分类统计
- 菜单：统计分析 → 分类统计
- 显示各分类商品数量占比饼图
- 了解商品分类分布情况

### 报表生成
- 菜单：统计分析 → 生成报表
- 生成详细的数据分析报表，包括：
  - 基本统计信息
  - 分类统计
  - 库存预警
  - 高价值商品排行
- 可导出为文本文件

## 高级功能

### 数据排序
- 点击表格列标题可对该列进行排序
- 支持按商品名称、分类、价格、库存等排序

### 快捷键
- `Ctrl+N`：新建文件
- `Ctrl+O`：打开文件
- `Ctrl+S`：保存文件
- `Ctrl+Shift+S`：另存为
- `Ctrl+A`：添加商品
- `Ctrl+E`：编辑商品
- `Delete`：删除商品
- `Ctrl+F`：搜索商品
- `F5`：刷新数据
- `Ctrl+Q`：退出程序

### 数据验证
系统会自动验证输入数据：
- 商品名称和分类不能为空
- 价格必须为非负数
- 库存必须为非负整数
- 输入错误时会显示友好的提示信息

## 示例数据

程序提供了多种格式的示例数据文件：

### CSV格式示例
- `sample_data.csv`：包含各种类型的商品信息
- 通过"文件 → 导入CSV"导入

### Excel格式示例
- `sample_products.xlsx`：包含更丰富的商品数据
- 通过"文件 → 导入Excel"导入
- 使用中文列名：商品名称、分类、价格、库存、供应商、描述

### Excel文件格式要求
导入Excel文件时，请确保包含以下必要列：
- **商品名称** 或 **name**（必填）
- **分类** 或 **category**（必填）
- **价格** 或 **price**（必填，数字格式）
- **库存** 或 **stock**（必填，整数格式）
- **供应商** 或 **supplier**（可选）
- **描述** 或 **description**（可选）

系统会自动：
- 识别中英文列名
- 验证数据格式
- 转换数据类型
- 过滤无效数据

## 故障排除

### 常见问题

**Q: 程序启动失败**
A: 请确保已安装所需的依赖包：
```bash
pip install -r requirements.txt
```

**Q: 中文显示乱码**
A: 确保系统支持UTF-8编码，文件保存时使用UTF-8编码

**Q: 图表显示异常**
A: 检查matplotlib是否正确安装，可能需要安装中文字体

**Q: 数据丢失**
A: 建议定期保存数据，程序退出时会提示保存未保存的数据

### 技术支持
如遇到其他问题，请检查：
1. Python版本是否为3.7+
2. 所有依赖包是否正确安装
3. 文件权限是否正确

## 系统要求

- **操作系统**：Windows 7+, macOS 10.12+, Linux
- **Python版本**：3.7或更高
- **内存**：至少512MB可用内存
- **磁盘空间**：至少100MB可用空间

## 数据安全

- 程序会自动创建SQLite数据库备份
- 建议定期导出数据作为备份
- 重要数据请及时保存到文件

---

*本指南涵盖了系统的主要功能，更多高级功能请探索菜单选项或查看源代码注释。*
