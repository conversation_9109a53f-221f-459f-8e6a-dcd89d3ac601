#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建Excel示例文件的脚本
"""

import pandas as pd

def create_sample_excel():
    """创建Excel示例文件"""
    
    # 示例数据
    sample_data = [
        {"商品名称": "iPhone 15 Pro", "分类": "电子产品", "价格": 7999.0, "库存": 25, "供应商": "苹果公司", "描述": "最新款专业智能手机"},
        {"商品名称": "Samsung Galaxy S24", "分类": "电子产品", "价格": 6999.0, "库存": 30, "供应商": "三星", "描述": "安卓旗舰手机"},
        {"商品名称": "华为MateBook", "分类": "电子产品", "价格": 5999.0, "库存": 15, "供应商": "华为", "描述": "轻薄笔记本电脑"},
        {"商品名称": "戴尔显示器", "分类": "电子产品", "价格": 1299.0, "库存": 40, "供应商": "戴尔", "描述": "27寸4K显示器"},
        {"商品名称": "罗技鼠标", "分类": "电子产品", "价格": 299.0, "库存": 100, "供应商": "罗技", "描述": "无线游戏鼠标"},
        
        {"商品名称": "耐克Air Max", "分类": "服装鞋帽", "价格": 1299.0, "库存": 60, "供应商": "Nike", "描述": "经典气垫运动鞋"},
        {"商品名称": "阿迪达斯外套", "分类": "服装鞋帽", "价格": 599.0, "库存": 80, "供应商": "Adidas", "描述": "运动休闲外套"},
        {"商品名称": "优衣库T恤", "分类": "服装鞋帽", "价格": 99.0, "库存": 200, "供应商": "优衣库", "描述": "纯棉基础T恤"},
        {"商品名称": "李宁运动裤", "分类": "服装鞋帽", "价格": 299.0, "库存": 120, "供应商": "李宁", "描述": "透气运动长裤"},
        
        {"商品名称": "蓝山咖啡豆", "分类": "食品饮料", "价格": 168.0, "库存": 50, "供应商": "星巴克", "描述": "进口精品咖啡豆"},
        {"商品名称": "龙井绿茶", "分类": "食品饮料", "价格": 288.0, "库存": 80, "供应商": "西湖茶叶", "描述": "正宗西湖龙井"},
        {"商品名称": "进口红酒", "分类": "食品饮料", "价格": 599.0, "库存": 30, "供应商": "法国酒庄", "描述": "法国原装进口红酒"},
        {"商品名称": "有机蜂蜜", "分类": "食品饮料", "价格": 128.0, "库存": 100, "供应商": "蜂农合作社", "描述": "纯天然有机蜂蜜"},
        
        {"商品名称": "实木书桌", "分类": "家具用品", "价格": 1899.0, "库存": 20, "供应商": "宜家", "描述": "北欧风格实木书桌"},
        {"商品名称": "人体工学椅", "分类": "家具用品", "价格": 2299.0, "库存": 15, "供应商": "Herman Miller", "描述": "专业办公椅"},
        {"商品名称": "LED台灯", "分类": "家具用品", "价格": 399.0, "库存": 60, "供应商": "飞利浦", "描述": "护眼LED台灯"},
        {"商品名称": "收纳箱", "分类": "家具用品", "价格": 89.0, "库存": 150, "供应商": "无印良品", "描述": "简约收纳整理箱"},
        
        {"商品名称": "Python编程指南", "分类": "图书文具", "价格": 89.0, "库存": 100, "供应商": "人民邮电出版社", "描述": "Python入门到精通"},
        {"商品名称": "数据科学手册", "分类": "图书文具", "价格": 128.0, "库存": 60, "供应商": "机械工业出版社", "描述": "数据分析实战指南"},
        {"商品名称": "晨光笔记本", "分类": "图书文具", "价格": 15.0, "库存": 300, "供应商": "晨光文具", "描述": "A5线圈笔记本"},
        {"商品名称": "得力文具套装", "分类": "图书文具", "价格": 59.0, "库存": 80, "供应商": "得力", "描述": "学生文具套装"},
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    
    # 保存为Excel文件
    excel_file = "sample_products.xlsx"
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"Excel示例文件已创建: {excel_file}")
    print(f"包含 {len(sample_data)} 条商品记录")
    
    # 显示文件内容预览
    print("\n文件内容预览:")
    print(df.head())
    
    return excel_file

if __name__ == "__main__":
    create_sample_excel()
