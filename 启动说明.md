# 商品信息管理系统 - 启动说明

## 快速启动

### 1. 环境要求
- Python 3.7 或更高版本
- Windows/macOS/Linux 操作系统

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动程序
```bash
python main.py
```

## 主要功能

- ✅ 商品信息管理（增删改查）
- ✅ 数据导入导出（CSV、Excel、JSON）
- ✅ 数据统计与可视化
- ✅ 完整的GUI界面操作

## 示例数据

程序提供了两个示例数据文件：
- `sample_data.csv` - CSV格式示例数据
- `sample_products.xlsx` - Excel格式示例数据

可通过"文件"菜单导入这些示例数据来快速体验系统功能。

## 详细文档

- `README.md` - 项目完整说明
- `使用指南.md` - 详细使用指南
- `Excel导入功能说明.md` - Excel导入功能说明
- `项目总结.md` - 项目总结报告

## 技术支持

如遇问题，请查看相关文档或检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 文件权限是否正确

---
**开发者**: 学生姓名  
**学号**: 学号  
**版本**: v1.0
