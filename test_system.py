#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品信息管理系统测试脚本
用于验证系统功能是否正常
"""

import unittest
import json
import os
import tempfile
from main import ProductManagementSystem

class TestProductManagementSystem(unittest.TestCase):
    """测试商品信息管理系统"""

    def setUp(self):
        """测试前准备"""
        # 创建临时测试数据
        self.test_product = {
            "name": "测试商品",
            "category": "测试分类",
            "price": 99.99,
            "stock": 50,
            "supplier": "测试供应商",
            "description": "这是一个测试商品"
        }

    def test_product_data_validation(self):
        """测试商品数据验证"""
        # 测试有效数据
        self.assertIsInstance(self.test_product["name"], str)
        self.assertIsInstance(self.test_product["price"], (int, float))
        self.assertIsInstance(self.test_product["stock"], int)
        self.assertGreaterEqual(self.test_product["price"], 0)
        self.assertGreaterEqual(self.test_product["stock"], 0)

    def test_json_file_operations(self):
        """测试JSON文件操作"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            test_data = [self.test_product]
            json.dump(test_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            # 测试读取
            with open(temp_file, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)

            self.assertEqual(len(loaded_data), 1)
            self.assertEqual(loaded_data[0]["name"], "测试商品")

        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)

    def test_data_statistics(self):
        """测试数据统计功能"""
        test_data = [
            {"name": "商品1", "category": "分类A", "price": 100, "stock": 10},
            {"name": "商品2", "category": "分类A", "price": 200, "stock": 20},
            {"name": "商品3", "category": "分类B", "price": 150, "stock": 15},
        ]

        # 计算统计数据
        total_products = len(test_data)
        total_value = sum(p["price"] * p["stock"] for p in test_data)
        avg_price = sum(p["price"] for p in test_data) / total_products
        categories = len(set(p["category"] for p in test_data))

        # 验证统计结果
        self.assertEqual(total_products, 3)
        self.assertEqual(total_value, 7250)  # 100*10 + 200*20 + 150*15 = 1000+4000+2250
        self.assertEqual(avg_price, 150)     # (100+200+150)/3
        self.assertEqual(categories, 2)      # 分类A, 分类B

    def test_search_functionality(self):
        """测试搜索功能"""
        test_data = [
            {"name": "iPhone", "category": "电子产品", "supplier": "苹果"},
            {"name": "MacBook", "category": "电子产品", "supplier": "苹果"},
            {"name": "Nike鞋", "category": "服装", "supplier": "Nike"},
        ]

        # 测试按名称搜索
        keyword = "iphone"
        results = [p for p in test_data if keyword.lower() in p["name"].lower()]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["name"], "iPhone")

        # 测试按分类搜索
        keyword = "电子"
        results = [p for p in test_data if keyword in p["category"]]
        self.assertEqual(len(results), 2)

        # 测试按供应商搜索
        keyword = "苹果"
        results = [p for p in test_data if keyword in p["supplier"]]
        self.assertEqual(len(results), 2)

def run_basic_tests():
    """运行基本功能测试"""
    print("=" * 50)
    print("商品信息管理系统 - 功能测试")
    print("=" * 50)

    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)

    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

def demo_data_operations():
    """演示数据操作功能"""
    print("\n演示数据操作功能:")
    print("-" * 30)

    # 示例商品数据
    products = [
        {"name": "iPhone 15", "category": "电子产品", "price": 5999, "stock": 50},
        {"name": "MacBook Pro", "category": "电子产品", "price": 12999, "stock": 20},
        {"name": "Nike运动鞋", "category": "服装鞋帽", "price": 899, "stock": 100},
    ]

    print(f"商品总数: {len(products)}")
    print(f"总价值: ¥{sum(p['price'] * p['stock'] for p in products):,.2f}")
    print(f"平均价格: ¥{sum(p['price'] for p in products) / len(products):.2f}")

    # 分类统计
    categories = {}
    for p in products:
        cat = p["category"]
        categories[cat] = categories.get(cat, 0) + 1

    print("\n分类统计:")
    for cat, count in categories.items():
        print(f"  {cat}: {count}件")

if __name__ == "__main__":
    try:
        # 运行测试
        run_basic_tests()

        # 演示功能
        demo_data_operations()

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
